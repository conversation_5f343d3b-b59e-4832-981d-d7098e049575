package com.shinet.core.safe.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Pair;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.coohua.user.event.api.dto.QueryRequest;
import com.coohua.user.event.api.remote.rpc.UserAdEcpmRpc;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.container.AppBuilder;
import com.shinet.core.safe.dto.BasicApp;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.dto.StrategyResult;
import com.shinet.core.safe.enums.StoreNameEnums;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.AndroidLockRst;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.ScreenRecordLog;
import com.shinet.core.safe.msql.entity.ScreenRecordReq;
import com.shinet.core.safe.msql.service.LockWhiteStrategyService;
import com.shinet.core.safe.msql.service.ScreenRecordLogServiceImpl;
import com.shinet.core.safe.msql.service.UserLoginRenewalService;
import com.shinet.core.safe.msql.service.androidlock.*;
import com.shinet.core.safe.msql.service.distributionconf.LockDistributionConfigService;
import com.shinet.core.safe.msql.service.ioslock.IpAliYunService;
import com.shinet.core.safe.msql.service.push.AndroidAppPushService;
import com.shinet.core.safe.msql.service.quickapp.QuickAppConfigService;
import com.shinet.core.safe.msql.service.sdkinit.SdkInitConfigService;
import com.shinet.core.safe.util.ReturnResult;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.shinet.core.safe.constant.GlobalConstants.SCREEN_RECORD_ERROR;
import static com.shinet.core.safe.msql.service.androidlock.AndroidLock2Service.getStoreName;

@Slf4j
@RestController
public class AndroidLockController {
    @Autowired
    AndroidLockService androidLockService;
    @Autowired
    AndroidLockRstService androidLockRstService;

    @Autowired
    SafeSwitcher safeSwitcher;
    @Autowired
    AndroidLock2Service androidLock2Service;

    @Autowired
    private ScreenRecordLogServiceImpl screenRecordLogService;
    @Resource
    private AndroidAppPushService appPushService;

    @MotanReferer(basicReferer = "user-event")
    private UserAdEcpmRpc userAdEcpmRpc;
    @Resource
    private OcpcLockService ocpcLockService;


    @Value("${screen.recording.usage.time:3600}")
    public Integer screenRecordingUsageTime;

    @Value("${screen.recording.min.pv:200}")
    public Integer screenRecordingMinPv;

    @ApolloJsonValue("${screen.recording.white.map}")
    public Map<String, String> screenRecordingWhiteMap;

    @ApolloJsonValue("${screen.recording.check.product}")
    private List<String> screenRecordCheckProductList;

    @Value("${screen.recording.oppo.switch:false}")
    private boolean screenRecordingOppoSwitch;

    @ApolloJsonValue("${screen.product.pv.time.map:{}}")
    public Map<String, String> productPvTimeMap;

    @ApolloJsonValue("${alternate.domain.white.product.list:[]}")
    public List<String> alternateDomainWhiteProductList;

    @Value("${enable.alternate.domain:false}")
    private boolean enableAlternateDomain;

    @Value("${alternate.domain.name}")
    private String alternateDomainName;


    @Resource(name = "stringRedisTemplate2")
    private StringRedisTemplate stringRedisTemplate2;

    @Autowired
    private UserLoginRenewalService userLoginRenewalService;

    @RequestMapping("/safe/adrd/lc")
    @ResponseBody
    public ReturnResult adrdLock(AndroidLokReq androidLokReq,HttpServletRequest request, HttpServletResponse response) {
        response.setHeader("X-Server-Timestamp", String.valueOf(System.currentTimeMillis()));
        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
        String ip  = IpUtils.getIpAddress(request);
        ReturnResult returnResult = androidLock2Service.checkLock2(androidLokReq, commonHeaderDTO, ip);
        return returnResult;
    }


    public static void main(String[] args){
        System.out.println(StringUtils.contains("oppo","vivo"));
        System.out.println(StringUtils.containsAny("天水","南京","杭州","深圳"));
    }
    @RequestMapping("/safe/otherPkg/info")
    @ResponseBody
    public ReturnResult getOtherPkgInfo(AndroidLokReq androidLokReq,HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        Set<String>  aset = androidLockService.getBlackPkgs(androidLokReq.getProduct(), androidLokReq.getChannel());
        returnResult.setData(aset);
        return returnResult;
    }

    @Autowired
    AndroidLockConfService androidLockConfService;
    /**
     * https://sapi.shinet.cn/sf/adfg?product=qzhcjbp&channel=qzhcjbphuawei&appVersion=0.0.1
     * 首层锁区
     * @param androidLokReq
     * @param request
     * @return
     */
    @RequestMapping("/sf/adfg")
    @ResponseBody
    public ReturnResult adfgSwitch(AndroidLokReq androidLokReq,HttpServletRequest request, HttpServletResponse response) {
        response.setHeader("X-Server-Timestamp", String.valueOf(System.currentTimeMillis()));
        StopWatch stopWatch = new StopWatch("adfgSwitch");
        stopWatch.start("ip&header");
        ReturnResult returnResult = new ReturnResult();
        String ip  = IpUtils.getIpAddress(request);

        // 获取请求头信息，用于用户登录续期
        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
        if (commonHeaderDTO.getAppId() == null) {
            BasicApp basicApp = AppBuilder.getByProduct(androidLokReq.getProduct());
            if (basicApp != null && basicApp.getAppId() != 0) {
                commonHeaderDTO.setAppId(String.valueOf(basicApp.getAppId()));
            } else {
                log.warn("refreshHbase未找到产品对应的 appId {}", com.alibaba.fastjson.JSONObject.toJSONString(androidLokReq));
            }
        }
        stopWatch.stop();

        stopWatch.start("AllLockCheck");
        StoreNameEnums storeNameEnums = getStoreName(androidLokReq.getProduct(),androidLokReq.getChannel());
        Pair<Boolean,String>  bpair = new Pair<Boolean,String>(false,"非ab渠道");
        if(StoreNameEnums.huawei.equals(storeNameEnums) ||
                StoreNameEnums.xiaomi.equals(storeNameEnums) ||
                StoreNameEnums.vivo.equals(storeNameEnums) ||
                StoreNameEnums.oppo.equals(storeNameEnums) ||
                StoreNameEnums.honor.equals(storeNameEnums)
        ){
            bpair = androidLockConfService.isLockByChannelVersion(androidLokReq.getProduct(),androidLokReq.getChannel(),androidLokReq.getAppVersion(),false,null);
        }
        stopWatch.stop();
//        if(StringUtils.isNotBlank(ip) && safeSwitcher.wtLockIps.contains(ip)){
//            AndroidLockRst androidLockRst = androidLockRstService.queryByIp(androidLokReq.getProduct(),ip,androidLokReq.getChannel());
//            if(androidLockRst!=null){
//                if(StringUtils.equalsIgnoreCase(androidLockRst.getLockFlag(),"true")){
//                    bpair = new Pair<Boolean,String>(true,"白名单ip rst");
//                    log.info("白名单ip rst首层进入 "+ip);
//                }else{
//                    bpair = new Pair<Boolean,String>(false,"白名单ip rst");
//                }
//            }
//        }
        stopWatch.start("saveRst");
        if(androidLokReq.getIsVp()){
            log.info("vpn开启 "+ip);
        }

        AndroidLockRst androidLockRst = new AndroidLockRst();
        if(bpair.getKey()){
            returnResult.setData(androidLokReq.getProduct(),1);
            androidLockRst.setLockFlag("true");

            androidLockRst.setIp(ip);
            androidLockRst.setIsOcpc(1);
            androidLockRst.setRemark(bpair.getValue());
            androidLockRst.setRemark2("android首层锁区");
            androidLockRstService.saveRst(androidLokReq,androidLockRst,true);
        }else{
            returnResult.setData(androidLokReq.getProduct(),0);
            Set<String>  aset = androidLockService.getBlackPkgs(androidLokReq.getProduct(), androidLokReq.getChannel());
            returnResult.setData("lcPkgs",aset);
            androidLockRst.setLockFlag("false");
        }
        stopWatch.stop();

        stopWatch.start("ipCity");

        Integer pkgSw = androidLockService.checkPkgSw(androidLockRst, storeNameEnums, androidLokReq, ip);
        returnResult.setData("pkgSw",pkgSw);

        stopWatch.stop();

        stopWatch.start("domain&renewal");

        // 下发域名 混淆
        if (enableAlternateDomain && StringUtils.isNotBlank(alternateDomainName)) {

            if (CollUtil.isEmpty(alternateDomainWhiteProductList) || alternateDomainWhiteProductList.contains(androidLokReq.getProduct())) {
                String s = Base64.getEncoder().encodeToString(alternateDomainName.getBytes(StandardCharsets.UTF_8));
                returnResult.setData("accessKey", s);
            }

        }

        returnResult.setData("pushKey", appPushService.getPushConfig(androidLokReq.getProduct()));

        // 异步执行用户登录续期
        userLoginRenewalService.renewUserAccessKeyAsync(
            commonHeaderDTO.getAccessKey(),
            commonHeaderDTO.getAppId(),
            "android"
        );
        stopWatch.stop();

        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if (totalTimeMillis > 500) {
            log.warn("一道锁耗时 slow request: {} ms, header {}, param: {}, ip {}",
                stopWatch.prettyPrint(TimeUnit.MILLISECONDS), commonHeaderDTO, androidLokReq, ip);
        }

        log.info("android ip锁adfg "+androidLokReq.getProduct()+"  "+androidLokReq.getChannel()+" "+ip+" "+bpair.getKey());
        return returnResult;
    }

    @Data
    class CaidVo {
        String caid;
        String ip;
        String city;

        public CaidVo(String vo) {
            String[] s = vo.split("_");
            this.caid = s[0];
            this.ip = s[1];
        }
    }
    @RequestMapping("/sf/test")
    public void test() {


        List<String> list = Arrays.asList("ff2bcf01d68ade716eb15744c81adbdb_112.96.80.239","ff2bcf01d68ade716eb15744c81adbdb_112.96.83.111","ff2bcf01d68ade716eb15744c81adbdb_112.96.80.47","ff2bcf01d68ade716eb15744c81adbdb_112.96.82.42","fda5b3d867b5b46273299206a20cc907_223.104.149.194","fda5b3d867b5b46273299206a20cc907_223.104.149.218","fce4ea3204ffcbb75dd05d04ade28fb5_36.113.114.208","fce4ea3204ffcbb75dd05d04ade28fb5_27.195.97.1","fce4ea3204ffcbb75dd05d04ade28fb5_120.225.81.35","fbf5353ebf98892bcd4768597e144962_27.154.192.131","fbf5353ebf98892bcd4768597e144962_27.149.76.46","fbf5353ebf98892bcd4768597e144962_124.72.59.129","f7f1bca8e9c3be5dda91d88de7c2c005_118.73.230.190","f7f1bca8e9c3be5dda91d88de7c2c005_60.221.101.162","f6bbb3fc279f987d0a25cbf2ca346e54_117.70.139.131","f6bbb3fc279f987d0a25cbf2ca346e54_111.39.38.188","f4b45f124e0c8d3612eea0dc78dcee16_120.243.139.232","f4b45f124e0c8d3612eea0dc78dcee16_39.144.160.230","f45fcf360e7c558c98807af9443be416_39.144.224.42","f45fcf360e7c558c98807af9443be416_119.86.162.207","f45fcf360e7c558c98807af9443be416_39.144.224.162","f45fcf360e7c558c98807af9443be416_183.227.249.132","f1ed14747a5457bca6b11902e6d1f0ab_111.11.89.4","f1ed14747a5457bca6b11902e6d1f0ab_120.211.216.58","f1c176a8927fe1c18232a3da63e8ebfe_223.104.72.237","f1c176a8927fe1c18232a3da63e8ebfe_119.2.244.164","f1c176a8927fe1c18232a3da63e8ebfe_223.104.85.128","f0deee40ea6f65a8847df0ea2244a1d0_42.97.225.115","f0deee40ea6f65a8847df0ea2244a1d0_123.246.57.164","f04e4890c41c0cfaf054626bf4115edc_223.155.134.103","f04e4890c41c0cfaf054626bf4115edc_120.227.232.190","f04e4890c41c0cfaf054626bf4115edc_111.55.102.51","ef8f7ff0812ec73c896fb22434287d33_120.70.213.173","ef8f7ff0812ec73c896fb22434287d33_110.153.83.101","eabbd3474f85912b455a01b06ae259ad_116.162.95.142","eabbd3474f85912b455a01b06ae259ad_116.162.95.78","e9f591cb6fc2941e734dd62073b6e067_106.115.80.160","e9f591cb6fc2941e734dd62073b6e067_124.127.25.144","e98220822150f52cdf3d425c7159232c_110.176.19.18","e98220822150f52cdf3d425c7159232c_110.176.30.203","e98220822150f52cdf3d425c7159232c_171.126.7.75","e98220822150f52cdf3d425c7159232c_183.187.30.191","e98220822150f52cdf3d425c7159232c_117.136.4.253","e789d08245b22586d1fd62b4dc398f4e_112.97.192.56","e789d08245b22586d1fd62b4dc398f4e_112.97.193.194","e5e282b6e163e461a1f09f375d8a4c9a_183.160.248.34","e5e282b6e163e461a1f09f375d8a4c9a_117.71.77.202","e51bea121e3391d53183a5be6864e63c_39.144.224.240","e51bea121e3391d53183a5be6864e63c_113.249.169.2","e49229e60772e37a4723e9b45af87e9a_106.61.222.144","e49229e60772e37a4723e9b45af87e9a_182.241.171.146","e29cf3c083dcf59dc3554b7bed314ed4_223.104.154.72","e29cf3c083dcf59dc3554b7bed314ed4_112.21.76.54","e2890d7b99da9b442427877c5254a044_60.221.113.125","e2890d7b99da9b442427877c5254a044_60.221.110.235","e2890d7b99da9b442427877c5254a044_60.221.113.29","e1ba82c4f4be4431afa018e8c8f2f914_106.87.114.123","e1ba82c4f4be4431afa018e8c8f2f914_39.144.221.119","e0f6ec102e965203996faaff74553ec4_223.104.40.116","e0f6ec102e965203996faaff74553ec4_114.251.228.250","e0b8e5a25b5f491d495721356a44460b_36.159.219.206","e0b8e5a25b5f491d495721356a44460b_39.144.161.185","dfd03c610e2a51292494e8b4ef195082_223.66.194.50","dfd03c610e2a51292494e8b4ef195082_223.66.195.190","dc55714e8ea847bc953c0a946cb5fddc_39.150.224.119","dc55714e8ea847bc953c0a946cb5fddc_223.104.110.111","db825d3037343833e4860f86cc188855_223.104.80.206","db825d3037343833e4860f86cc188855_223.104.88.146","db825d3037343833e4860f86cc188855_120.231.24.181","d82bde2aa96841ad50ea263436498fc0_106.87.114.226","d82bde2aa96841ad50ea263436498fc0_119.86.61.131","d82bde2aa96841ad50ea263436498fc0_14.110.88.170","d2732d420ddeb225b5b156b4d9f9cd8c_223.104.148.68","d2732d420ddeb225b5b156b4d9f9cd8c_39.144.153.174","d2732d420ddeb225b5b156b4d9f9cd8c_49.75.44.105","d2732d420ddeb225b5b156b4d9f9cd8c_223.104.4.56","d05aab5f7c7baec578aa1860bc2d300c_117.181.61.104","d05aab5f7c7baec578aa1860bc2d300c_116.1.230.240","ce7ee90c2625b0f7b8179cbb654886ef_223.64.6.135","ce7ee90c2625b0f7b8179cbb654886ef_112.20.115.204","ce1908c3f8ef977089e23949af70edd4_39.144.192.254","ce1908c3f8ef977089e23949af70edd4_39.144.192.230","ce1908c3f8ef977089e23949af70edd4_42.49.55.106","cb47d1d3dedc57716811abc9e8e3e4a6_60.19.225.206","cb47d1d3dedc57716811abc9e8e3e4a6_42.84.238.122","cb47d1d3dedc57716811abc9e8e3e4a6_42.84.232.11","c80ac8d8c9cb798f3dd690e234faf08f_36.98.148.36","c80ac8d8c9cb798f3dd690e234faf08f_106.119.25.248","c43234514f4e84b470cfaf84721997b3_182.240.232.185","c43234514f4e84b470cfaf84721997b3_119.62.213.227","c1fb34f1d8c71e26553af264d2345342_182.118.236.98","c1fb34f1d8c71e26553af264d2345342_182.118.236.100","c0969d77bfff2e670e3bfd61b8826a45_39.144.112.17","c0969d77bfff2e670e3bfd61b8826a45_111.15.152.13","bf7d8aef44b2227c61e60187a5fe5681_218.94.71.78","bf7d8aef44b2227c61e60187a5fe5681_39.144.156.82","bf7d8aef44b2227c61e60187a5fe5681_39.144.156.90","bf7d8aef44b2227c61e60187a5fe5681_112.20.68.100","bee434eed295ae6a67e7026ca6dc5d6b_39.144.180.37","bee434eed295ae6a67e7026ca6dc5d6b_39.144.180.53","be76a41a2754b8bd02439eb2acd7f21a_113.194.193.64","be76a41a2754b8bd02439eb2acd7f21a_106.224.202.131","b7a5e575d07e8ba99c34839c12d213fb_121.206.183.190","b7a5e575d07e8ba99c34839c12d213fb_120.35.245.204","b0e010011b5bb6b28ac1b2792325ba66_125.43.76.133","b0e010011b5bb6b28ac1b2792325ba66_125.43.76.143","af58b235053d0ac2f6518f5d73816296_220.200.127.46","af58b235053d0ac2f6518f5d73816296_219.232.77.26","af446a51660418b089c6ff3cffce2a58_39.144.49.98","af446a51660418b089c6ff3cffce2a58_39.144.49.122","af446a51660418b089c6ff3cffce2a58_106.119.67.38","aec7137e1f854fcde16b99d7dcb324b4_111.55.223.53","aec7137e1f854fcde16b99d7dcb324b4_111.55.223.51","abf61c394d1ae593c5463120367a29c1_60.220.28.125","abf61c394d1ae593c5463120367a29c1_218.26.54.244","a9d7a2b9751f11d0454a836bee702d35_117.140.191.144","a9d7a2b9751f11d0454a836bee702d35_180.139.230.67","a9952bd2c624d18b7a8845336f1053be_113.246.245.187","a9952bd2c624d18b7a8845336f1053be_113.240.147.167","a8b12606aeb89a75d4c91556c6deb342_60.27.104.57","a8b12606aeb89a75d4c91556c6deb342_60.27.104.196","a8b12606aeb89a75d4c91556c6deb342_117.9.17.244","a856bbd0ec1306d13b476b7a9472d068_117.95.1.99","a856bbd0ec1306d13b476b7a9472d068_122.96.32.11","a7fcfc228b3fef4b96a464f3dbfc002c_223.104.73.196","a7fcfc228b3fef4b96a464f3dbfc002c_223.104.73.200","a7fcfc228b3fef4b96a464f3dbfc002c_183.234.228.204","a4f13962d2118982a7c1d90648537084_39.144.50.190","a4f13962d2118982a7c1d90648537084_39.144.50.178","a20b0bb5232c0209850445b339a48cc0_14.156.45.94","a20b0bb5232c0209850445b339a48cc0_183.57.242.111","a112405e142e4929b40258d106b316ab_222.174.14.25","a112405e142e4929b40258d106b316ab_223.104.195.235","9b49729b29a84aca3adfde44632789e6_183.42.242.165","9b49729b29a84aca3adfde44632789e6_112.96.114.204","993452512d65eb950decea6115d7fd80_175.5.79.70","993452512d65eb950decea6115d7fd80_116.162.226.203","96bee1f51e4e04a63f9466a339a9d8ed_39.163.22.51","96bee1f51e4e04a63f9466a339a9d8ed_117.136.36.212","96a830e88aa1e7873824e84e7b5c388b_117.136.104.23","96a830e88aa1e7873824e84e7b5c388b_39.144.179.71","96a830e88aa1e7873824e84e7b5c388b_39.149.221.187","94a757a351d95b398e5ae3b6b1ca4203_123.167.71.155","94a757a351d95b398e5ae3b6b1ca4203_42.102.138.232","94a757a351d95b398e5ae3b6b1ca4203_42.102.183.9","94a757a351d95b398e5ae3b6b1ca4203_123.167.41.138","92c9f6b81e11a0fc2020f6eba427a77a_39.144.225.114","92c9f6b81e11a0fc2020f6eba427a77a_39.144.225.116","90f03aa87cedd3c5c1689db83882b36e_112.17.242.36","90f03aa87cedd3c5c1689db83882b36e_112.17.242.2","90f03aa87cedd3c5c1689db83882b36e_112.17.242.60","90f03aa87cedd3c5c1689db83882b36e_112.17.242.26","8cceea3af67a13a84f525faa7e949c1f_119.44.45.246","8cceea3af67a13a84f525faa7e949c1f_111.55.103.119","8b7c1859a604941891dd9bc98acf1835_112.39.62.50","8b7c1859a604941891dd9bc98acf1835_112.39.243.188","89ce4c45c46a5c33345282ba42b4d53b_117.136.112.132","89ce4c45c46a5c33345282ba42b4d53b_39.191.198.193","878cbcfe740cba1d5ef7049695e8fedf_182.145.169.218","878cbcfe740cba1d5ef7049695e8fedf_182.129.130.151","831c9083137db2696abf5c5da4bcb8f2_117.174.64.3","831c9083137db2696abf5c5da4bcb8f2_223.160.191.60","8258bc54bb461910cd7aa5c6c90c87c9_116.169.10.129","8258bc54bb461910cd7aa5c6c90c87c9_175.154.34.103","7fc611a1fa8aed1340169a73b5c5f722_14.31.98.133","7fc611a1fa8aed1340169a73b5c5f722_14.212.55.222","7fc611a1fa8aed1340169a73b5c5f722_14.213.27.197","7fbe27cde9f768e466f7e30be35b60e2_223.97.48.175","7fbe27cde9f768e466f7e30be35b60e2_112.7.75.71","7fbe27cde9f768e466f7e30be35b60e2_223.104.195.28","7e6ef22424b5b801313dabd0be17bfa6_121.34.227.189","7e6ef22424b5b801313dabd0be17bfa6_113.110.140.250","7d2da14ee84337543f5efd48a00b2417_171.88.4.62","7d2da14ee84337543f5efd48a00b2417_182.148.158.24","76c5bbaf609bddb4a14162b6cd87a781_223.160.230.39","76c5bbaf609bddb4a14162b6cd87a781_223.160.228.231","76c5bbaf609bddb4a14162b6cd87a781_223.160.231.119","76282219396b844f97bb1b59894eba0e_223.104.164.18","76282219396b844f97bb1b59894eba0e_223.104.164.10","76282219396b844f97bb1b59894eba0e_123.158.254.58","76282219396b844f97bb1b59894eba0e_39.144.122.74","76282219396b844f97bb1b59894eba0e_39.144.122.82","76282219396b844f97bb1b59894eba0e_123.158.254.250","754332414fcef0ba939b249a71ad7b31_121.225.183.3","754332414fcef0ba939b249a71ad7b31_114.229.134.148","72859bfded32199648bd2224ebb84b89_223.104.71.69","72859bfded32199648bd2224ebb84b89_116.26.133.50","72859bfded32199648bd2224ebb84b89_223.104.71.66","71aca8ae4a53e038888413c4e8521aaf_49.83.156.69","71aca8ae4a53e038888413c4e8521aaf_39.144.151.86","71aca8ae4a53e038888413c4e8521aaf_223.104.149.115","71aca8ae4a53e038888413c4e8521aaf_223.104.149.139","70c634a2339d59d8c7edc44db087397a_113.225.10.181","70c634a2339d59d8c7edc44db087397a_39.144.57.250","6f51d4b82206b6b565f9f58d54aaa097_182.204.42.158","6f51d4b82206b6b565f9f58d54aaa097_42.248.7.58","6ed39b225bb8a6233d337147adf6580a_114.232.82.94","6ed39b225bb8a6233d337147adf6580a_153.101.162.187","6cbc1a3c2ca394c1694000732c2b21ea_183.197.32.210","6cbc1a3c2ca394c1694000732c2b21ea_183.197.32.171","6c9df1de04bbf5632af2aa176e203efe_112.97.166.22","6c9df1de04bbf5632af2aa176e203efe_113.89.104.205","67bc75ffab28887ca5549bf6691cedd1_39.144.225.98","67bc75ffab28887ca5549bf6691cedd1_14.105.96.25","67bc75ffab28887ca5549bf6691cedd1_39.144.225.100","66e59a5037293ce759c6e898b7c9d2ec_171.108.205.247","66e59a5037293ce759c6e898b7c9d2ec_171.108.202.42","650d957b60f43ba4842600541b7bb5d9_116.5.184.29","650d957b60f43ba4842600541b7bb5d9_112.97.197.9","6456392a86e44f5394c2c804f903462e_222.213.122.117","6456392a86e44f5394c2c804f903462e_116.169.7.237","6456392a86e44f5394c2c804f903462e_116.169.7.243","6451a624087346bb4e5e58090df7b9bb_223.104.54.76","6451a624087346bb4e5e58090df7b9bb_223.104.55.125","6182c79a95003701d44a7088d3bedca6_117.136.90.66","6182c79a95003701d44a7088d3bedca6_113.26.47.152","60a97de0a8763ff3818fc06ec3f05d0a_111.55.35.219","60a97de0a8763ff3818fc06ec3f05d0a_42.243.15.224","60a97de0a8763ff3818fc06ec3f05d0a_39.129.255.73","60a97de0a8763ff3818fc06ec3f05d0a_106.59.202.224","5fa8c8a2db96218099a817edc3d9739a_39.144.246.245","5fa8c8a2db96218099a817edc3d9739a_39.144.246.249","5fa8c8a2db96218099a817edc3d9739a_124.235.69.168","5fa50bd60a27872acca8e9c488b4da12_115.214.150.171","5fa50bd60a27872acca8e9c488b4da12_36.21.195.90","5eb637f9cae2e3ce1c44c81dbe4b95c6_124.79.73.57","5eb637f9cae2e3ce1c44c81dbe4b95c6_39.144.104.245","5eb637f9cae2e3ce1c44c81dbe4b95c6_39.144.244.46","5eb637f9cae2e3ce1c44c81dbe4b95c6_223.104.5.44","5e7913c2b3744f2747748fd54c0b387a_180.139.204.5","5e7913c2b3744f2747748fd54c0b387a_180.138.233.70","5e7913c2b3744f2747748fd54c0b387a_223.104.93.212","5d2063bc0780cfc2f236061b4b12c009_106.33.165.26","5d2063bc0780cfc2f236061b4b12c009_123.163.247.55","5c6f5ad0a6673b20a37f79ebb3272429_39.144.168.222","5c6f5ad0a6673b20a37f79ebb3272429_39.144.168.199","5bb04afc6601a8850caff1f9f55df8ad_223.104.166.133","5bb04afc6601a8850caff1f9f55df8ad_223.104.166.23","5b188dedc535a6c5198a89aeffbb0c16_39.144.85.164","5b188dedc535a6c5198a89aeffbb0c16_39.144.85.168","5a07c2b54d03d4ceb8ca7deacf852a66_117.61.184.191","5a07c2b54d03d4ceb8ca7deacf852a66_117.61.184.127","59497f25806927461c0fd2ebcd6613e0_112.224.190.194","59497f25806927461c0fd2ebcd6613e0_112.8.215.226","59497f25806927461c0fd2ebcd6613e0_39.87.55.215","585592a4da5293070d64d8b2a7b8ae4c_183.0.213.51","585592a4da5293070d64d8b2a7b8ae4c_183.46.2.236","5786d715e87410ad5457acbd4a778809_116.10.89.109","5786d715e87410ad5457acbd4a778809_124.226.51.6","535204a81f06983f446dc6815fb5aeab_118.212.215.60","535204a81f06983f446dc6815fb5aeab_106.7.19.5","535204a81f06983f446dc6815fb5aeab_106.224.13.31","535204a81f06983f446dc6815fb5aeab_118.212.215.217","5325dfa9203476e730a0a84a4860e24f_39.144.197.193","5325dfa9203476e730a0a84a4860e24f_223.115.96.11","51fc1f66e2c100461c938df6b5f72fb8_219.159.189.89","51fc1f66e2c100461c938df6b5f72fb8_117.182.167.106","4e6da5c2943838381406f1cefc5a76c0_113.132.61.109","4e6da5c2943838381406f1cefc5a76c0_117.136.51.67","4a50eee953a72e0df7331fb0c44e5895_39.144.54.16","4a50eee953a72e0df7331fb0c44e5895_112.42.78.40","4a50eee953a72e0df7331fb0c44e5895_124.93.12.42","4972c7809809f0e20a05a453edf3020b_223.104.85.169","4972c7809809f0e20a05a453edf3020b_223.104.85.181","4972c7809809f0e20a05a453edf3020b_119.142.131.45","4972c7809809f0e20a05a453edf3020b_119.143.43.93","47d9e69b3fc4703821be88b85785124c_120.228.79.111","47d9e69b3fc4703821be88b85785124c_116.162.93.172","47d9e69b3fc4703821be88b85785124c_116.162.93.108","450e7a49eb5bd32e1386287b3fe11145_223.104.86.244","450e7a49eb5bd32e1386287b3fe11145_14.218.126.127","448592fd1bcb68ad1b44675c347c8490_112.224.195.51","448592fd1bcb68ad1b44675c347c8490_123.128.234.90","4185277af8bd9ce7a71841428c8251bd_117.136.112.163","4185277af8bd9ce7a71841428c8251bd_39.180.89.226","40b4dde9db4ade81744a4477107f542f_111.183.105.192","40b4dde9db4ade81744a4477107f542f_116.210.96.123","40b4dde9db4ade81744a4477107f542f_111.183.3.149","3ec5f63dff329b17f6cccce2f44b3940_218.26.157.158","3ec5f63dff329b17f6cccce2f44b3940_110.176.97.176","3deadd75b3ed013ac40872677f6ccd8a_125.94.190.206","3deadd75b3ed013ac40872677f6ccd8a_223.104.71.96","3d44629c10116777372c5b362c1beed9_112.111.24.54","3d44629c10116777372c5b362c1beed9_112.5.89.11","3d44629c10116777372c5b362c1beed9_112.111.24.120","3d44629c10116777372c5b362c1beed9_112.111.24.26","3d44629c10116777372c5b362c1beed9_223.104.55.4","3d44629c10116777372c5b362c1beed9_112.111.24.118","3cc4199c511ca21ccf8ef4f3113da312_113.195.58.184","3cc4199c511ca21ccf8ef4f3113da312_182.102.155.148","3b330ae4b6045f0f8e6140298901713c_223.104.68.16","3b330ae4b6045f0f8e6140298901713c_223.104.68.23","3a3fe5ce7d10018c3d25f846c4793262_112.44.16.194","3a3fe5ce7d10018c3d25f846c4793262_39.144.139.63","3813caee8b1b3c8abe1847102a878ff9_14.16.88.230","3813caee8b1b3c8abe1847102a878ff9_223.74.122.171","36525da66b26e54e3d71cd09099eacb1_36.248.175.102","36525da66b26e54e3d71cd09099eacb1_175.44.19.16","2e888d5a505c4c345979cf7be4233bb5_49.94.165.151","2e888d5a505c4c345979cf7be4233bb5_114.227.115.146","2d29a76d939a344ec291b15170cc4e68_39.144.151.172","2d29a76d939a344ec291b15170cc4e68_180.125.119.180","2bf330cc1ce4863179cb6e4d02d173e3_111.55.166.176","2bf330cc1ce4863179cb6e4d02d173e3_113.12.73.224","2bf330cc1ce4863179cb6e4d02d173e3_36.136.49.205","2bf330cc1ce4863179cb6e4d02d173e3_117.141.141.59","2af006c43907b843407d8683d5913055_223.104.196.226","2af006c43907b843407d8683d5913055_223.104.196.212","25fda2f468c55065329d6a1fce671695_112.0.142.14","25fda2f468c55065329d6a1fce671695_49.93.212.50","2335bad6c4a074b8cd7bf5695647f27a_39.144.231.81","2335bad6c4a074b8cd7bf5695647f27a_117.188.16.36","21e66f108ecb2b41394932dd6aa448b7_39.144.250.182","21e66f108ecb2b41394932dd6aa448b7_183.251.193.140","203c776476f788c1bc6c4345cde6aecf_171.82.58.61","203c776476f788c1bc6c4345cde6aecf_223.104.119.126","1f5b86afbdf5c46b1cdd169798012a09_106.86.99.174","1f5b86afbdf5c46b1cdd169798012a09_27.10.163.47","1d9f8ca0e2ef72618f5165747085aee8_39.144.170.196","1d9f8ca0e2ef72618f5165747085aee8_117.178.59.132","1be2a731a5216ca1467ca835c8b57e3e_36.6.234.200","1be2a731a5216ca1467ca835c8b57e3e_36.6.234.148","1bc65b53c1252653ab87dc7ac141fa7a_116.162.92.173","1bc65b53c1252653ab87dc7ac141fa7a_116.162.92.109","1aa10e203ac4534b16dea09f84829b59_39.144.181.240","1aa10e203ac4534b16dea09f84829b59_39.144.181.160","1aa10e203ac4534b16dea09f84829b59_39.148.31.247","198866776998c8371acd073f4aa43aa0_180.139.230.249","198866776998c8371acd073f4aa43aa0_115.46.142.169","1451880224b03180c7b5a8a3ae0af4f0_111.58.176.250","1451880224b03180c7b5a8a3ae0af4f0_111.58.177.81","100fe7b7f43e0c15885c7aa5fb72c715_106.113.88.33","100fe7b7f43e0c15885c7aa5fb72c715_106.113.88.153","100fe7b7f43e0c15885c7aa5fb72c715_183.199.111.88","0ebadfadb092c6e3c4c5a1e7aa105f46_222.179.226.2","0ebadfadb092c6e3c4c5a1e7aa105f46_113.251.180.169","0cb695faf249d1af2c7a53fd0a42d00f_110.180.32.130","0cb695faf249d1af2c7a53fd0a42d00f_110.180.32.248","0c657c56dd1d71dc7a23e221c35c767b_223.104.122.208","0c657c56dd1d71dc7a23e221c35c767b_223.104.122.214","0c657c56dd1d71dc7a23e221c35c767b_27.28.244.18","0af4f2ebac05e947135b4e019af1e8bf_111.17.134.143","0af4f2ebac05e947135b4e019af1e8bf_39.144.108.1","0af4f2ebac05e947135b4e019af1e8bf_39.144.108.26","07c27c290d2cc5b94e9796038708b360_117.183.186.50","07c27c290d2cc5b94e9796038708b360_117.183.187.174","07c27c290d2cc5b94e9796038708b360_117.183.187.179","064e6c1c9ea0a0418a70e3737b72e12c_223.85.186.186","064e6c1c9ea0a0418a70e3737b72e12c_223.85.184.154","0142912a0354eac0d7a9bcc11f825559_39.144.161.116","0142912a0354eac0d7a9bcc11f825559_183.209.201.238","0142912a0354eac0d7a9bcc11f825559_122.96.32.119","008de651e5e24a040598aeb2995e4763_111.37.36.110","008de651e5e24a040598aeb2995e4763_223.104.195.10");
        List<CaidVo> collect = list.stream().map(vo -> new CaidVo(vo)).collect(Collectors.toList());
        Map<String, List<CaidVo>> collect1 = collect.stream().collect(Collectors.groupingBy(caidVo -> caidVo.caid));
        Set<String> caidSet = new HashSet<>();
        collect1.forEach((k,v)->{
            for (CaidVo caidVo : v) {
                String city = "";
                Pair<String, String> cityd = IpAliYunService.getIpAddress(
                        "zzdf",
                        caidVo.getCaid(),
                        caidVo.getIp(),
                        "test"
                );
                city = cityd != null ? cityd.getValue() : city;
                caidVo.setCity(city);
            }
        });
        System.out.println(JSONUtil.toJsonStr(collect));
    }


    /**
     * 录屏限制检测
     * @param screenRecordReq
     * @param request
     * @return
     */
    @PostMapping(value = "/safe/screenRecord/limit")
    @ResponseBody
    public ReturnResult screenRecordLimitCheck(@RequestBody ScreenRecordReq screenRecordReq, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        returnResult.setData("enabledScreenRecordCheck", "false");
        ScreenRecordLog recordLog = new ScreenRecordLog();
        log.info("录屏限制检验开始：{}", screenRecordReq);

        if (StringUtils.isBlank(screenRecordReq.getProduct())) {
            returnResult.setMessage("产品名称不能为空");
            returnResult.setStatus(SCREEN_RECORD_ERROR);
            log.info("录屏限制检测产品名称不能为空");
            return returnResult;
        }

        if (StringUtils.isBlank(screenRecordReq.getDeviceId())) {
            returnResult.setMessage("设备ID不能为空");
            returnResult.setStatus(SCREEN_RECORD_ERROR);
            log.info("录屏限制检测设备ID不能为空");
            return returnResult;
        }

        if (screenRecordReq.getUserId() == null) {
            returnResult.setMessage("用户ID不能为空");
            returnResult.setStatus(SCREEN_RECORD_ERROR);
            log.info("录屏限制检测用户ID不能为空");
            return returnResult;
        }

        if (screenRecordReq.getAppId() == null) {
            returnResult.setMessage("appId不能为空");
            returnResult.setStatus(SCREEN_RECORD_ERROR);
            log.info("录屏限制检测appId不能为空");
            return returnResult;
        }

        StoreNameEnums storeNameEnums = getStoreName(screenRecordReq.getProduct(), screenRecordReq.getChannel());

        // 判断是否需要检测
        if (CollUtil.isNotEmpty(screenRecordCheckProductList) && screenRecordCheckProductList.contains(screenRecordReq.getProduct())) {
            log.info("{} 该产品录屏限制检验不执行，未配置放行：{}", screenRecordReq.getProduct(), screenRecordReq);
            recordLog.setRemark("该产品录屏限制检验不执行，未配置放行");
            screenRecordLogService.saveAsync(recordLog, screenRecordReq);
            returnResult.setMessage("当前产品未配置放行");
            return returnResult;
        }

        returnResult.setData("enabledScreenRecordCheck", "true");

        Integer screenTime = screenRecordingUsageTime;
        Integer screenPv = screenRecordingMinPv;
        // 获取当前产品的组名
        String groupName = stringRedisTemplate2.opsForValue().get("productTeamKey:"+screenRecordReq.getProduct());
        log.info("当前产品的组名 {} {}", screenRecordReq.getProduct(), groupName);
        if (StringUtils.isNotBlank(groupName)) {
            //根据组名获取time和pv
            String groupTimePvStr = productPvTimeMap.get(groupName);
            if (StringUtils.isNotBlank(groupTimePvStr)) {
                String[] split = groupTimePvStr.split(":");
                screenTime = Integer.parseInt(split[0]);
                screenPv = Integer.parseInt(split[1]);
            }
        }
        // 根据产品获取time和pv
        String timePvStr = productPvTimeMap.get(screenRecordReq.getProduct());
        if (StringUtils.isNotBlank(timePvStr)) {
            String[] split = timePvStr.split(":");
            screenTime = Integer.parseInt(split[0]);
            screenPv = Integer.parseInt(split[1]);
        }
        returnResult.setData("screenRecordingUsageTime", screenTime);
        returnResult.setData("screenRecordingMinPv", screenPv);

        recordLog.setConfigUsageTime(screenTime);
        recordLog.setConfigPv(screenPv);

        String ip  = IpUtils.getIpAddress(request);
        recordLog.setIp(ip);

        // oppo渠道 审核时可以录屏
        if (enableOppoScreenRecord(returnResult, screenRecordReq, storeNameEnums, recordLog)) {
            screenRecordLogService.saveAsync(recordLog, screenRecordReq);
            return returnResult;
        }

        // 判断白名单
        if(screenRecordingWhiteMap.containsKey(screenRecordReq.getOaid())){
            log.info("录屏白名单命中：{} {} {}", screenRecordReq.getOaid(), ip, screenRecordReq.getUserId());
            returnResult.setData("hitWhiteListFlag",true);
            returnResult.setData("pv", 0);
            recordLog.setHitWhiteListFlag("true");
            returnResult.setData("screenRecordingUsageTime", 0);
            returnResult.setData("screenRecordingMinPv", 0);

            recordLog.setHitPvFlag("false");
            recordLog.setPv(0);
            recordLog.setRemark("录屏白名单命中");
            screenRecordLogService.saveAsync(recordLog, screenRecordReq);
            return returnResult;
        } else {
            returnResult.setData("hitWhiteListFlag",false);
            recordLog.setHitWhiteListFlag("false");
        }

        // 调用user-event查询当前用户的pv
        int pv = getUserPv(screenRecordReq);

        returnResult.setData("hitPvFlag", pv >= screenPv);
        returnResult.setData("screenRecordingMinPv", screenPv);
        returnResult.setData("pv", pv);
        returnResult.setData("screenRecordingUsageTime", screenTime);

        recordLog.setPv(pv);
        recordLog.setHitPvFlag(String.valueOf(pv > screenPv));

        if (pv > screenPv) {
            recordLog.setRemark("录屏pv命中");
        } else {

            recordLog.setRemark("录屏白名单未命中，录屏pv未命中");
        }


        screenRecordLogService.saveAsync(recordLog, screenRecordReq);

        log.info("录屏限制检验结束：{}", returnResult);
        return returnResult;
    }

    private boolean enableOppoScreenRecord(ReturnResult returnResult, ScreenRecordReq screenRecordReq, StoreNameEnums storeNameEnums, ScreenRecordLog recordLog) {
        if ((storeNameEnums.equals(StoreNameEnums.oppo))
                && screenRecordingOppoSwitch) {
            log.info("oppo/huawei审核时可以录屏：{} {}", screenRecordReq.getDeviceId(), screenRecordReq.getUserId());
            returnResult.setData("hitWhiteListFlag",true);
            returnResult.setData("pv", 0);
            recordLog.setHitWhiteListFlag("true");
            returnResult.setData("screenRecordingUsageTime", 0);
            returnResult.setData("screenRecordingMinPv", 0);

            recordLog.setHitPvFlag("false");
            recordLog.setPv(0);
            recordLog.setRemark("oppo/huawei审核时可以录屏");
            return true;
        }
        return false;
    }

    /**
     * 获取用户pv数
     * @param screenRecordReq
     * @return
     */
    private int getUserPv(ScreenRecordReq screenRecordReq) {
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setOs(screenRecordReq.getOs());
        queryRequest.setAppId(screenRecordReq.getAppId());
        queryRequest.setUserIdList(Collections.singletonList(screenRecordReq.getUserId()));

        String str = userAdEcpmRpc.queryUserECPMBatch(queryRequest);
        log.info("录屏检测rpc查询用户pv返回：{}", str);

        if (StringUtils.isBlank(str)) return 0;

        JSONObject jsonObject = JSONUtil.parseObj(str);
        Map map = jsonObject.get(screenRecordReq.getUserId().toString(), Map.class);


        if (CollUtil.isEmpty(map)) {
            log.info("录屏检测rpc查询用户pv返回为空：{}", str);
            return 0;
        }

        Object videoExposureCount = map.get("videoExposureCount");

        if (videoExposureCount != null) {
            return Integer.parseInt(videoExposureCount.toString());
        }

        return 0;

    }


}
