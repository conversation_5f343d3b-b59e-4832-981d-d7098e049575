package com.shinet.core.safe.msql.entity;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class CommonHeaderDTO implements Serializable {
    private String deviceId;
    private String brand;
    private String gps;
    private String bs;
    private String appVersion;
    private String product;
    private String os;
    private String channel;
    private String romVersion;
    private String osVersion;
    private String osLevel;
    private String accessKey;
    private String wechatId;
    private String pkgId;
    private String appId;
    private String ip;
    private String sdkVersion;


    private String imei;
    private String oaid;
    private String androidId;
    private String mac;
    private String model;
    private String ua;
    private String caid;
    private String idfa;
    private String openId;
    private String realIp;
    private String timestamp;
    private String adbState1;
    private String adbState2;
    private String devState;

    public Integer getIntOs(){
        try {
            return os.equals("android") ? 0 : 1;
        }catch (Exception e){
            return 0;
        }
    }

    public Map<String,String> getMap(){
        Map<String,String> headMap = new HashMap<>();
        headMap.put("deviceId",deviceId);
        headMap.put("brand",brand);
        headMap.put("gps",gps);
        headMap.put("bs",bs);
        headMap.put("appVersion",appVersion);
        headMap.put("os",os);
        headMap.put("channel",channel);
        headMap.put("romVersion",romVersion);
        headMap.put("osVersion",osVersion);
        headMap.put("accessKey",accessKey);
        headMap.put("wechatId",wechatId);
        headMap.put("pkgId",pkgId);
        headMap.put("appId",appId);
        return headMap;

    }

    public static CommonHeaderDTO getCommHead(AliUserDevice aliUserDevice){
        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        commonHeaderDTO.setAccessKey(aliUserDevice.getAccessKey());
        commonHeaderDTO.setAppId(aliUserDevice.getAppId()+"");
        commonHeaderDTO.setDeviceId(aliUserDevice.getDeviceId());
        commonHeaderDTO.setOs(aliUserDevice.getOs());

        commonHeaderDTO.setAppVersion(aliUserDevice.getAppVersion());
        commonHeaderDTO.setBrand(aliUserDevice.getBrand());
        commonHeaderDTO.setGps(aliUserDevice.getGps());
        commonHeaderDTO.setChannel(aliUserDevice.getChannel());
        commonHeaderDTO.setOsVersion(aliUserDevice.getOsVersion());
        commonHeaderDTO.setRomVersion(aliUserDevice.getRomVersion());
        commonHeaderDTO.setPkgId(aliUserDevice.getPkgId());

        return commonHeaderDTO;
    }

    public Long  getUserId(){
        if (StringUtils.isEmpty(this.accessKey)){
            return 0L;
        }
        String []result = this.accessKey.split("_");
        if (result.length <= 1){
            return 0L;
        }
        return  Long.parseLong(result[1]);
    }

    public static Long getUserId(CommonHeaderDTO commonHeaderDTO){
        return  getUserIdNoEx(commonHeaderDTO);
    }

    public static Long getUserIdNoEx(CommonHeaderDTO commonHeaderDTO){
        try {
            return  Long.parseLong(commonHeaderDTO.getAccessKey().split("_")[1]);
        }catch (Exception e){
            return 0L;
        }
    }
}
