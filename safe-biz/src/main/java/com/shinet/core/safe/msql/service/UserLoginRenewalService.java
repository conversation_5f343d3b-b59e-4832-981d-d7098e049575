package com.shinet.core.safe.msql.service;

import com.coohua.bp.user.remote.api.UserRPC;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 用户登录续期服务
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class UserLoginRenewalService {

    @MotanReferer(basicReferer = "bp-user")
    private UserRPC userRPC;

    /**
     * 用户登录续期专用线程池
     */
    private static final ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();

    static {
        taskExecutor.setCorePoolSize(4);
        taskExecutor.setMaxPoolSize(10);
        taskExecutor.setQueueCapacity(800);
        taskExecutor.setKeepAliveSeconds(60);
        taskExecutor.setThreadNamePrefix("UserLoginRenewal-");
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskExecutor.initialize();

        log.info("用户登录续期线程池初始化完成: core={}, max={}, queue={}, keepAlive={}s",
                4, 10, 800, 60);
    }

    /**
     * 异步续期用户登录AccessKey
     *
     * @param accessKey 用户访问密钥
     * @param appId     应用ID
     * @param os        操作系统类型，默认为android
     */
    public void renewUserAccessKeyAsync(String accessKey, String appId, String os) {
        // 参数验证
        if (StringUtils.isBlank(accessKey)) {
//            log.info("用户登录续期跳过：accessKey为空");
            return;
        }

        if (StringUtils.isBlank(appId)) {
//            log.info("用户登录续期跳过：appId为空，accessKey={}", accessKey);
            return;
        }

        // 使用自定义线程池异步执行续期操作
        taskExecutor.execute(() -> {
            try {
                // 调用UserRPC的renewAccessKey方法进行续期
                String result = String.valueOf(userRPC.renewAccessKey(accessKey, Long.valueOf(appId), os));

//                log.info("用户登录续期完成：accessKey={}, appId={}, os={}, result={}",
//                        accessKey, appId, os, result);

            } catch (Exception e) {
                log.error("用户登录续期失败：accessKey={}, appId={}, os={}, error={}",
                        accessKey, appId, os, e.getMessage(), e);
            }
        });
    }
}
