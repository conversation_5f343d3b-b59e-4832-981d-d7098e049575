package com.shinet.core.safe.service;

import com.coohua.user.event.api.remote.rpc.UserGrayRpc;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserBlockService {

    @MotanReferer(basicReferer = "user-event")
    private UserGrayRpc userGrayRpc;

    public void blockUser() {
        try {
            userGrayRpc.doBlockUser();
        } catch (Exception e) {
            log.warn("");
        }
    }

}
