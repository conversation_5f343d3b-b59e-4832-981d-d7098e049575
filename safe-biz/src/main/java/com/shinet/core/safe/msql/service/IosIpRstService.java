package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.msql.entity.IosIpRst;
import com.shinet.core.safe.msql.mapper.IosIpRstMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-03-29
*/
@Service
public class IosIpRstService extends ServiceImpl<IosIpRstMapper, IosIpRst> {

    public void saveIosRst(String product,String ip,boolean isLock,String city,String remark){
        if(StringUtils.isNotBlank(product) && StringUtils.isNotBlank(ip)){
            try {
                List<IosIpRst> ipRstList = lambdaQuery().eq(IosIpRst::getProduct,product).eq(IosIpRst::getIp,ip).list();
                IosIpRst iosIpRst = new IosIpRst();
                iosIpRst.setProduct(product);
                iosIpRst.setIp(ip);
                iosIpRst.setCity(city);
                iosIpRst.setLockFlag(isLock?1:0);
                iosIpRst.setRemark(remark);
                iosIpRst.setCreateTime(new Date());
                if(ipRstList.size()>0){
                    if(ipRstList.get(0).getRemark().length() > 60000) {
                        ipRstList.get(0).setRemark(StringUtils.substringBefore(ipRstList.get(0).getRemark(), ","));
                    }
                    IosIpRst iosIpRst1 = ipRstList.get(0);
                    iosIpRst1.setRemark(iosIpRst1.getRemark()+","+iosIpRst.getRemark());
                    iosIpRst1.setLockFlag(iosIpRst.getLockFlag());
                    iosIpRst1.setUpdateTime(new Date());

                    updateById(iosIpRst1);
                }else{
                    iosIpRst.setUpdateTime(new Date());
                    save(iosIpRst);
                }
            }catch (Exception e){
                log.error("",e);
            }
        }
    }

    public IosIpRst queryIosRst(String product,String ip){
        if(StringUtils.isNotBlank(product) && StringUtils.isNotBlank(ip)) {
            List<IosIpRst> ipRstList = lambdaQuery().eq(IosIpRst::getProduct, product).eq(IosIpRst::getIp, ip).list();
            if(ipRstList.size()>0){
                return ipRstList.get(0);
            }
        }
        return null;
    }

    public List<IosIpRst>  queryIosRsts(String product){
        Date date  = new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_DAY);
        List<IosIpRst> ipRstList = lambdaQuery().eq(IosIpRst::getProduct, product).gt(IosIpRst::getCreateTime,date).list();
        return ipRstList;
    }

}
